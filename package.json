{"name": "orebishop", "version": "0.1.0", "private": true, "dependencies": {"@reduxjs/toolkit": "^1.9.2", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "firebase": "^11.10.0", "framer-motion": "^8.0.2", "razorpay": "^2.9.6", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^4.7.1", "react-paginate": "^8.1.4", "react-redux": "^8.0.5", "react-router-dom": "^6.6.0", "react-scripts": "5.0.1", "react-slick": "^0.29.0", "react-toastify": "^11.0.5", "redux-persist": "^6.0.0", "slick-carousel": "^1.8.1", "tailwind-scrollbar": "^2.1.0", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.13", "postcss": "^8.4.20", "tailwindcss": "^3.2.4"}}