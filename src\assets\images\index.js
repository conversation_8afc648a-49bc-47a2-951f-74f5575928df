import logo from "./orebiLogo.png";
import logoLight from "./logoLight.png";
import bannerImgOne from "./banner/bannerImgOne.webp";
import bannerImgTwo from "./banner/bannerImgTwo.webp";
import bannerImgThree from "./banner/bannerImgThree.webp";
import saleImgOne from "./sale/saleImgOne.webp";
import saleImgTwo from "./sale/saleImgTwo.webp";
import saleImgThree from "./sale/saleImgThree.webp";
// ============== Products Start here ====================
// New Arrivals
import newArrOne from "./products/newArrival/newArrOne.webp";
import newArrTwo from "./products/newArrival/newArrTwo.webp";
import newArrThree from "./products/newArrival/newArrThree.webp";
import newArrFour from "./products/newArrival/newArrFour.webp";

// Best Sellers
import bestSellerOne from "./products/bestSeller/bestSellerOne.webp";
import bestSellerTwo from "./products/bestSeller/bestSellerTwo.webp";
import bestSellerThree from "./products/bestSeller/bestSellerThree.webp";
import bestSellerFour from "./products/bestSeller/bestSellerFour.webp";

// Special Offers
import spfOne from "./products/specialOffer/spfOne.webp";
import spfTwo from "./products/specialOffer/spfTwo.webp";
import spfThree from "./products/specialOffer/spfThree.webp";
import spfFour from "./products/specialOffer/spfFour.webp";

// Year Product
import productOfTheYear from "./products/productOfTheYear.webp";
// ============== Products End here ======================
import paymentCard from "./payment.png";
import emptyCart from "../images/emptyCart.png";

export {
  logo,
  logoLight,
  bannerImgOne,
  bannerImgTwo,
  bannerImgThree,
  saleImgOne,
  saleImgTwo,
  saleImgThree,
  // ===================== Products start here ============
  // New Arrivals
  newArrOne,
  newArrTwo,
  newArrThree,
  newArrFour,

  // Best Sellers
  bestSellerOne,
  bestSellerTwo,
  bestSellerThree,
  bestSellerFour,

  // Sprcial Offers
  spfOne,
  spfTwo,
  spfThree,
  spfFour,

  // Year Product
  productOfTheYear,
  // ===================== Products End here ==============
  paymentCard,
  emptyCart,
};
